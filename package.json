{"name": "trit<PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "env-cmd -f .env.dev vite build", "build:qa": "env-cmd -f .env.qa vite build", "build:uat": "env-cmd -f .env.uat vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@microsoft/signalr": "^8.0.7", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.77.1", "axios": "^1.9.0", "bootstrap": "5.3.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-pro-sidebar": "^1.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "redux-persist": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}