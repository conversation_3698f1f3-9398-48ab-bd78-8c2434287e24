import React, { useState } from 'react';
import { <PERSON>Mail, <PERSON><PERSON>ock, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ye<PERSON>ff, <PERSON><PERSON>ser, FiPhone } from 'react-icons/fi';

const Register = ({ onSuccess }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      onSuccess && onSuccess();
    }, 1500);
  };

  return (
    <div className="auth-form-container">
      <div className="auth-form-header">
        <h2 className="auth-title">Create Account</h2>
        <p className="auth-subtitle">Join us today and start your journey</p>
      </div>

      <form onSubmit={handleSubmit} className="auth-form">
        <div className="row">
          <div className="col-md-6">
            <div className="form-group">
              <label htmlFor="firstName" className="form-label">
                First Name
              </label>
              <div className="input-group">
                <div className="input-icon">
                  <FiUser size={20} />
                </div>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  className="form-control auth-input"
                  placeholder="First name"
                  value={formData.firstName}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-group">
              <label htmlFor="lastName" className="form-label">
                Last Name
              </label>
              <div className="input-group">
                <div className="input-icon">
                  <FiUser size={20} />
                </div>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  className="form-control auth-input"
                  placeholder="Last name"
                  value={formData.lastName}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="email" className="form-label">
            Email Address
          </label>
          <div className="input-group">
            <div className="input-icon">
              <FiMail size={20} />
            </div>
            <input
              type="email"
              id="email"
              name="email"
              className="form-control auth-input"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="phone" className="form-label">
            Phone Number
          </label>
          <div className="input-group">
            <div className="input-icon">
              <FiPhone size={20} />
            </div>
            <input
              type="tel"
              id="phone"
              name="phone"
              className="form-control auth-input"
              placeholder="Enter your phone number"
              value={formData.phone}
              onChange={handleChange}
              required
            />
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <div className="input-group">
            <div className="input-icon">
              <FiLock size={20} />
            </div>
            <input
              type={showPassword ? 'text' : 'password'}
              id="password"
              name="password"
              className="form-control auth-input"
              placeholder="Create password"
              value={formData.password}
              onChange={handleChange}
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <FiEyeOff size={20} /> : <FiEye size={20} />}
            </button>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="confirmPassword" className="form-label">
            Confirm Password
          </label>
          <div className="input-group">
            <div className="input-icon">
              <FiLock size={20} />
            </div>
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              id="confirmPassword"
              name="confirmPassword"
              className="form-control auth-input"
              placeholder="Confirm password"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <FiEyeOff size={20} /> : <FiEye size={20} />}
            </button>
          </div>
        </div>

        <div className="form-check mb-3">
          <input
            type="checkbox"
            className="form-check-input"
            id="terms"
            required
          />
          <label className="form-check-label" htmlFor="terms">
            I agree to the <a href="#" className="auth-link">Terms of Service</a> and{' '}
            <a href="#" className="auth-link">Privacy Policy</a>
          </label>
        </div>

        <button
          type="submit"
          className="btn btn-primary auth-submit-btn"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Creating Account...
            </>
          ) : (
            'Create Account'
          )}
        </button>

        <div className="auth-footer">
          <p>
            Already have an account?{' '}
            <span className="auth-link">Sign In</span>
          </p>
        </div>
      </form>
    </div>
  );
};

export default Register;
