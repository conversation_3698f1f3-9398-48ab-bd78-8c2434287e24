import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import AuthLayout from "@layouts/AuthLayout";
import MainLayout from "@layouts/MainLayout";
import Home from "@pages/Home";

const AppRouter = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Default route: show AuthLayout */}
        <Route path="/" element={<AuthLayout />} />

        {/* Protected or main route after auth */}
        <Route path="/home" element={<MainLayout />}>
          <Route index element={<Home />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
