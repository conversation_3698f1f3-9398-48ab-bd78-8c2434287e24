/* Modern Auth Layout */
.modern-auth-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Left Sidebar */
.auth-sidebar {
  width: 280px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.sidebar-content {
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-section {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.logo-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 1rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.nav-tabs {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.nav-tab {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: transparent;
  border: 2px solid transparent;
  border-radius: 12px;
  color: #718096;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.nav-tab:hover {
  background: #f7fafc;
  color: #4a5568;
  transform: translateX(5px);
}

.nav-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
  transform: translateX(10px);
}

.nav-tab svg {
  margin-right: 0.75rem;
}

/* Center Illustration Section */
.auth-center {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.illustration-container {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.illustration-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.illustration-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 3rem;
  max-width: 400px;
  line-height: 1.6;
}

/* Illustration Graphics */
.illustration-graphic {
  position: relative;
  width: 300px;
  height: 300px;
  margin: 0 auto;
}

.envelope-container {
  position: relative;
  width: 200px;
  height: 150px;
  margin: 0 auto;
}

.envelope {
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transform: rotate(-5deg);
  animation: float 3s ease-in-out infinite;
}

.envelope-flap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #e2e8f0;
  clip-path: polygon(0 0, 100% 0, 50% 100%);
  border-radius: 8px 8px 0 0;
}

.envelope-body {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 90px;
  background: white;
  border-radius: 0 0 8px 8px;
}

.envelope-letter {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  background: #f7fafc;
  border-radius: 4px;
  border: 2px solid #e2e8f0;
}

.checkmark-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 50px;
  height: 50px;
  background: #48bb78;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
  animation: pulse 2s infinite;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.float-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: floatAround 6s ease-in-out infinite;
}

.element-1 {
  width: 60px;
  height: 60px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 40px;
  height: 40px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.element-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: rotate(-5deg) translateY(0px); }
  50% { transform: rotate(-5deg) translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes floatAround {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

/* Right Form Section */
.auth-form-section {
  width: 450px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: -5px 0 30px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.form-wrapper {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

/* Form Styling */
.auth-form-container {
  width: 100%;
}

.auth-form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.auth-subtitle {
  color: #718096;
  font-size: 0.95rem;
  margin: 0;
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.9rem;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: #a0aec0;
  z-index: 2;
  pointer-events: none;
}

.auth-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: #ffffff;
}

.auth-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.auth-input::placeholder {
  color: #a0aec0;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #667eea;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check-input {
  margin-right: 0.5rem;
  accent-color: #667eea;
}

.form-check-label {
  color: #4a5568;
  cursor: pointer;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #5a67d8;
  text-decoration: underline;
}

.auth-submit-btn {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.auth-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.auth-footer {
  text-align: center;
  color: #718096;
  font-size: 0.9rem;
}

.auth-link {
  color: #667eea;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* Success Component */
.success-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.success-content {
  text-align: center;
  color: white;
  max-width: 500px;
}

.success-icon {
  margin-bottom: 2rem;
  color: #48bb78;
  animation: successPulse 2s ease-in-out infinite;
}

.success-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.success-message {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.success-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 3rem;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.feature-icon {
  color: #48bb78;
}

.success-btn {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.success-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .modern-auth-container {
    flex-direction: column;
  }

  .auth-sidebar {
    width: 100%;
    height: auto;
    order: 1;
  }

  .sidebar-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
  }

  .nav-tabs {
    flex-direction: row;
    margin-top: 0;
    gap: 0.5rem;
  }

  .nav-tab {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .nav-tab.active,
  .nav-tab:hover {
    transform: none;
  }

  .auth-center {
    order: 2;
    min-height: 300px;
  }

  .illustration-title {
    font-size: 2rem;
  }

  .illustration-subtitle {
    font-size: 1rem;
  }

  .auth-form-section {
    width: 100%;
    order: 3;
  }
}

@media (max-width: 768px) {
  .sidebar-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .logo-section {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
  }

  .nav-tabs {
    width: 100%;
    justify-content: center;
  }

  .auth-center {
    min-height: 250px;
    padding: 1rem;
  }

  .illustration-title {
    font-size: 1.75rem;
  }

  .illustration-subtitle {
    font-size: 0.95rem;
    margin-bottom: 2rem;
  }

  .illustration-graphic {
    width: 250px;
    height: 250px;
  }

  .form-wrapper {
    padding: 1.5rem;
  }

  .auth-title {
    font-size: 1.75rem;
  }

  .success-title {
    font-size: 2rem;
  }

  .success-features {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .form-wrapper {
    padding: 1rem;
  }

  .auth-title {
    font-size: 1.5rem;
  }

  .success-container {
    padding: 1rem;
  }

  .success-title {
    font-size: 1.75rem;
  }

  .success-message {
    font-size: 1rem;
  }
}
