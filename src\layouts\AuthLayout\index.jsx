// AuthLayout.jsx
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { FiShield, FiUsers } from 'react-icons/fi';
import Login from '../../components/Auth/Login';
import Register from '../../components/Auth/Register';
import Success from '../../components/Auth/Success';
import '../../assets/css/Custom.css';

const AuthLayout = () => {
  const [activeTab, setActiveTab] = useState('login'); // 'login', 'register', 'success'
  const containerRef = useRef(null);
  const formRef = useRef(null);
  const centerRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (formRef.current) {
      gsap.fromTo(formRef.current,
        { opacity: 0, x: activeTab === 'login' ? -50 : 50 },
        { opacity: 1, x: 0, duration: 0.6, ease: 'power2.out' }
      );
    }
  }, [activeTab]);

  const handleTabChange = (tab) => {
    if (tab === activeTab) return;

    gsap.to(formRef.current, {
      opacity: 0,
      x: tab === 'login' ? 50 : -50,
      duration: 0.3,
      onComplete: () => {
        setActiveTab(tab);
      },
    });
  };

  const handleSuccess = () => {
    gsap.to([formRef.current, centerRef.current], {
      opacity: 0,
      scale: 0.9,
      duration: 0.5,
      onComplete: () => setActiveTab('success'),
    });
  };

  const handleContinue = () => {
    navigate('/home');
  };

  return (
    <div className="modern-auth-container" ref={containerRef}>
      {/* Left Sidebar */}
      <div className="auth-sidebar">
        <div className="sidebar-content">
          <div className="logo-section">
            <div className="logo-icon">
              <FiShield size={32} />
            </div>
            <h3 className="logo-text">VMS</h3>
          </div>

          <div className="nav-tabs">
            <button
              className={`nav-tab ${activeTab === 'login' ? 'active' : ''}`}
              onClick={() => handleTabChange('login')}
            >
              <FiShield size={20} />
              <span>Sign In</span>
            </button>
            <button
              className={`nav-tab ${activeTab === 'register' ? 'active' : ''}`}
              onClick={() => handleTabChange('register')}
            >
              <FiUsers size={20} />
              <span>Sign Up</span>
            </button>
          </div>
        </div>
      </div>

      {/* Center Illustration Section */}
      {activeTab !== 'success' && (
        <div className="auth-center" ref={centerRef}>
          <div className="illustration-container">
            <div className="illustration-content">
              <h2 className="illustration-title">Start Your Journey.</h2>
              <p className="illustration-subtitle">
                Join thousands of users who trust our platform for their daily needs.
              </p>

              <div className="illustration-graphic">
                <div className="graphic-element main-shape">
                  <div className="envelope-container">
                    <div className="envelope">
                      <div className="envelope-flap"></div>
                      <div className="envelope-body"></div>
                      <div className="envelope-letter"></div>
                    </div>
                    <div className="checkmark-badge">
                      <FiShield size={24} />
                    </div>
                  </div>
                </div>

                <div className="floating-elements">
                  <div className="float-element element-1"></div>
                  <div className="float-element element-2"></div>
                  <div className="float-element element-3"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Right Form Section */}
      <div className="auth-form-section">
        <div className="form-wrapper" ref={formRef}>
          {activeTab === 'login' && <Login onSuccess={handleSuccess} />}
          {activeTab === 'register' && <Register onSuccess={handleSuccess} />}
          {activeTab === 'success' && <Success onContinue={handleContinue} />}
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
