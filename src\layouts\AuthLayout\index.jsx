// AuthLayout.jsx
import React, { useState, useRef, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { gsap } from 'gsap';
const AuthLayout = () => {
  const [activeTab, setActiveTab] = useState('login'); // 'login', 'register', 'success'
  const containerRef = useRef(null);
  const contentRef = useRef(null);

  useEffect(() => {
    if (contentRef.current) {
      gsap.fromTo(contentRef.current, 
        { opacity: 0, x: activeTab === 'login' ? -100 : 100 }, 
        { opacity: 1, x: 0, duration: 0.5, ease: 'power2.out' }
      );
    }
  }, [activeTab]);

  const handleSuccess = () => {
    gsap.to(contentRef.current, {
      opacity: 0,
      x: -100,
      duration: 0.5,
      onComplete: () => setActiveTab('success'),
    });
  };

  return (
    <div className="auth-container" ref={containerRef}>
      <div className="sidebar">
        <button onClick={() => setActiveTab('login')}>Sign In</button>
        <button onClick={() => setActiveTab('register')}>Sign Up</button>
      </div>

      <div className="content" ref={contentRef}>
        {/* {activeTab === 'login' && <Login />}
        {activeTab === 'register' && <Register onSuccess={handleSuccess} />} */}
        {activeTab === 'success' && (
          <div className="success-panel">
            <h2>Start Your Journey.</h2>
            <p>Lorem ipsum dolor sit amet.</p>
            <div className="checkmark">✔️</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthLayout;
