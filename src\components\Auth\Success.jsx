import React from 'react';
import { FiCheckCircle, FiArrowRight } from 'react-icons/fi';

const Success = ({ onContinue }) => {
  return (
    <div className="success-container">
      <div className="success-content">
        <div className="success-icon">
          <FiCheckCircle size={80} />
        </div>
        
        <h2 className="success-title">Welcome Aboard!</h2>
        <p className="success-message">
          Your account has been created successfully. You're now ready to start your journey with us.
        </p>
        
        <div className="success-features">
          <div className="feature-item">
            <div className="feature-icon">
              <FiCheckCircle size={20} />
            </div>
            <span>Account verified</span>
          </div>
          <div className="feature-item">
            <div className="feature-icon">
              <FiCheckCircle size={20} />
            </div>
            <span>Profile setup complete</span>
          </div>
          <div className="feature-item">
            <div className="feature-icon">
              <FiCheckCircle size={20} />
            </div>
            <span>Ready to explore</span>
          </div>
        </div>

        <button 
          className="btn btn-primary success-btn"
          onClick={onContinue}
        >
          Get Started
          <FiArrowRight size={20} className="ms-2" />
        </button>
      </div>
    </div>
  );
};

export default Success;
